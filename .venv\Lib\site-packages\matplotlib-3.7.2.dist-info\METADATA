Metadata-Version: 2.1
Name: matplotlib
Version: 3.7.2
Summary: Python plotting package
Home-page: https://matplotlib.org
Download-URL: https://matplotlib.org/stable/users/installing/index.html
Author: <PERSON>, <PERSON>
Author-email: <EMAIL>
License: PSF
Project-URL: Documentation, https://matplotlib.org
Project-URL: Source Code, https://github.com/matplotlib/matplotlib
Project-URL: Bug Tracker, https://github.com/matplotlib/matplotlib/issues
Project-URL: Forum, https://discourse.matplotlib.org/
Project-URL: Donate, https://numfocus.org/donate-to-matplotlib
Platform: any
Classifier: Development Status :: 5 - Production/Stable
Classifier: Framework :: Matplotlib
Classifier: Intended Audience :: Science/Research
Classifier: Intended Audience :: Education
Classifier: License :: OSI Approved :: Python Software Foundation License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Topic :: Scientific/Engineering :: Visualization
Requires-Python: >=3.8
Description-Content-Type: text/markdown
License-File: LICENSE\LICENSE
License-File: LICENSE\LICENSE_AMSFONTS
License-File: LICENSE\LICENSE_BAKOMA
License-File: LICENSE\LICENSE_CARLOGO
License-File: LICENSE\LICENSE_COLORBREWER
License-File: LICENSE\LICENSE_COURIERTEN
License-File: LICENSE\LICENSE_JSXTOOLS_RESIZE_OBSERVER
License-File: LICENSE\LICENSE_QHULL
License-File: LICENSE\LICENSE_QT4_EDITOR
License-File: LICENSE\LICENSE_SOLARIZED
License-File: LICENSE\LICENSE_STIX
License-File: LICENSE\LICENSE_YORICK
Requires-Dist: contourpy (>=1.0.1)
Requires-Dist: cycler (>=0.10)
Requires-Dist: fonttools (>=4.22.0)
Requires-Dist: kiwisolver (>=1.0.1)
Requires-Dist: numpy (>=1.20)
Requires-Dist: packaging (>=20.0)
Requires-Dist: pillow (>=6.2.0)
Requires-Dist: pyparsing (<3.1,>=2.3.1)
Requires-Dist: python-dateutil (>=2.7)
Requires-Dist: importlib-resources (>=3.2.0) ; python_version<"3.10"

[![PyPi](https://badge.fury.io/py/matplotlib.svg)](https://badge.fury.io/py/matplotlib)
[![Downloads](https://pepy.tech/badge/matplotlib/month)](https://pepy.tech/project/matplotlib)
[![NUMFocus](https://img.shields.io/badge/powered%20by-NumFOCUS-orange.svg?style=flat&colorA=E1523D&colorB=007D8A)](https://numfocus.org)

[![DiscourseBadge](https://img.shields.io/badge/help_forum-discourse-blue.svg)](https://discourse.matplotlib.org)
[![Gitter](https://badges.gitter.im/matplotlib/matplotlib.svg)](https://gitter.im/matplotlib/matplotlib)
[![GitHubIssues](https://img.shields.io/badge/issue_tracking-github-blue.svg)](https://github.com/matplotlib/matplotlib/issues)
[![GitTutorial](https://img.shields.io/badge/PR-Welcome-%23FF8300.svg?)](https://git-scm.com/book/en/v2/GitHub-Contributing-to-a-Project)

[![GitHubActions](https://github.com/matplotlib/matplotlib/workflows/Tests/badge.svg)](https://github.com/matplotlib/matplotlib/actions?query=workflow%3ATests)
[![AzurePipelines](https://dev.azure.com/matplotlib/matplotlib/_apis/build/status/matplotlib.matplotlib?branchName=main)](https://dev.azure.com/matplotlib/matplotlib/_build/latest?definitionId=1&branchName=main)
[![AppVeyor](https://ci.appveyor.com/api/projects/status/github/matplotlib/matplotlib?branch=main&svg=true)](https://ci.appveyor.com/project/matplotlib/matplotlib)
[![Codecov](https://codecov.io/github/matplotlib/matplotlib/badge.svg?branch=main&service=github)](https://codecov.io/github/matplotlib/matplotlib?branch=main)

![image](https://matplotlib.org/_static/logo2.svg)

Matplotlib is a comprehensive library for creating static, animated, and
interactive visualizations in Python.

Check out our [home page](https://matplotlib.org/) for more information.

![image](https://matplotlib.org/_static/readme_preview.png)

Matplotlib produces publication-quality figures in a variety of hardcopy
formats and interactive environments across platforms. Matplotlib can be
used in Python scripts, Python/IPython shells, web application servers,
and various graphical user interface toolkits.

## Install

See the [install
documentation](https://matplotlib.org/stable/users/installing/index.html),
which is generated from `/doc/users/installing/index.rst`

## Contribute

You've discovered a bug or something else you want to change -
excellent!

You've worked out a way to fix it -- even better!

You want to tell us about it -- best of all!

Start at the [contributing
guide](https://matplotlib.org/devdocs/devel/contributing.html)!

## Contact

[Discourse](https://discourse.matplotlib.org/) is the discussion forum
for general questions and discussions and our recommended starting
point.

Our active mailing lists (which are mirrored on Discourse) are:

-   [Users](https://mail.python.org/mailman/listinfo/matplotlib-users)
    mailing list: <<EMAIL>>
-   [Announcement](https://mail.python.org/mailman/listinfo/matplotlib-announce)
    mailing list: <<EMAIL>>
-   [Development](https://mail.python.org/mailman/listinfo/matplotlib-devel)
    mailing list: <<EMAIL>>

[Gitter](https://gitter.im/matplotlib/matplotlib) is for coordinating
development and asking questions directly related to contributing to
matplotlib.

## Citing Matplotlib

If Matplotlib contributes to a project that leads to publication, please
acknowledge this by citing Matplotlib.

[A ready-made citation
entry](https://matplotlib.org/stable/users/project/citing.html) is
available.
