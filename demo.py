#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像拼接与布局优化系统演示程序
展示系统的核心功能
"""

import cv2
import numpy as np
import os
import sys
import time

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.image_processor import ImageProcessor
from src.feature_matcher import FeatureMatcher
from src.image_stitcher import ImageStitcher
from src.layout_optimizer import LayoutOptimizer
from src.image_searcher import ImageSearcher


def create_demo_images():
    """创建演示图像"""
    print("🎨 创建演示图像...")
    
    demo_dir = "demo_images"
    if not os.path.exists(demo_dir):
        os.makedirs(demo_dir)
    
    # 创建更复杂的演示图像
    images_info = [
        {"color": (220, 20, 60), "name": "红色风景", "pattern": "landscape"},
        {"color": (34, 139, 34), "name": "绿色森林", "pattern": "forest"},
        {"color": (30, 144, 255), "name": "蓝色海洋", "pattern": "ocean"},
        {"color": (255, 215, 0), "name": "金色沙漠", "pattern": "desert"},
    ]
    
    image_paths = []
    
    for i, info in enumerate(images_info):
        # 创建基础图像
        img = np.full((300, 400, 3), info["color"], dtype=np.uint8)
        
        # 添加渐变效果
        for y in range(300):
            factor = y / 300.0
            img[y, :] = img[y, :] * (0.7 + 0.3 * factor)
        
        # 添加几何图案
        if info["pattern"] == "landscape":
            # 山峰轮廓
            points = np.array([[0, 200], [100, 150], [200, 180], [300, 120], [400, 160], [400, 300], [0, 300]], np.int32)
            cv2.fillPoly(img, [points], (139, 69, 19))
        elif info["pattern"] == "forest":
            # 树木图案
            for x in range(50, 350, 60):
                cv2.rectangle(img, (x, 200), (x+20, 280), (101, 67, 33), -1)
                cv2.circle(img, (x+10, 180), 25, (0, 100, 0), -1)
        elif info["pattern"] == "ocean":
            # 波浪图案
            for y in range(150, 300, 20):
                for x in range(0, 400, 40):
                    cv2.ellipse(img, (x, y), (20, 8), 0, 0, 180, (255, 255, 255), 2)
        elif info["pattern"] == "desert":
            # 沙丘图案
            for x in range(0, 400, 80):
                points = np.array([[x, 250], [x+40, 200], [x+80, 250], [x+80, 300], [x, 300]], np.int32)
                cv2.fillPoly(img, [points], (238, 203, 173))
        
        # 添加标题
        cv2.putText(img, info["name"], (20, 40), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        # 添加特征点
        for j in range(15):
            x = np.random.randint(50, 350)
            y = np.random.randint(80, 250)
            cv2.circle(img, (x, y), 3, (255, 255, 255), -1)
            cv2.circle(img, (x, y), 8, (0, 0, 0), 1)
        
        # 保存图像
        filename = os.path.join(demo_dir, f"demo_{i+1}_{info['pattern']}.jpg")
        cv2.imwrite(filename, img)
        image_paths.append(filename)
        print(f"  ✅ 创建 {info['name']} - {filename}")
    
    return image_paths


def demo_feature_matching():
    """演示特征匹配功能"""
    print("\n🔍 演示特征匹配功能")
    print("-" * 40)
    
    # 创建演示图像
    image_paths = create_demo_images()
    
    # 加载图像
    processor = ImageProcessor()
    images = [processor.load_image(path) for path in image_paths[:2]]
    
    # 测试不同算法
    algorithms = ['SIFT', 'ORB', 'AKAZE']
    
    for algorithm in algorithms:
        print(f"\n📊 {algorithm} 算法分析:")
        
        try:
            matcher = FeatureMatcher(algorithm=algorithm)
            
            # 检测特征点
            start_time = time.time()
            kp1, desc1 = matcher.detect_and_compute(images[0])
            kp2, desc2 = matcher.detect_and_compute(images[1])
            detection_time = time.time() - start_time
            
            print(f"  特征检测时间: {detection_time:.3f}秒")
            print(f"  图像1特征点: {len(kp1) if kp1 else 0}个")
            print(f"  图像2特征点: {len(kp2) if kp2 else 0}个")
            
            # 匹配特征点
            if desc1 is not None and desc2 is not None:
                start_time = time.time()
                matches = matcher.match_features(desc1, desc2)
                matching_time = time.time() - start_time
                
                print(f"  特征匹配时间: {matching_time:.3f}秒")
                print(f"  匹配点数量: {len(matches)}个")
                
                # 计算相似度
                similarity = matcher.compute_similarity(desc1, desc2)
                print(f"  图像相似度: {similarity:.3f}")
                
                # 保存匹配可视化
                if len(matches) > 0:
                    match_img = matcher.visualize_matches(images[0], kp1, images[1], kp2, matches[:20])
                    output_path = f"demo_images/matches_{algorithm.lower()}.jpg"
                    cv2.imwrite(output_path, match_img)
                    print(f"  匹配结果保存: {output_path}")
            
        except Exception as e:
            print(f"  ❌ {algorithm} 测试失败: {str(e)}")


def demo_image_stitching():
    """演示图像拼接功能"""
    print("\n🖼️  演示图像拼接功能")
    print("-" * 40)
    
    # 加载演示图像
    image_paths = create_demo_images()
    processor = ImageProcessor()
    images = [processor.load_image(path) for path in image_paths[:3]]
    
    # 标准化图像尺寸
    images = processor.normalize_images(images)
    
    # 创建拼接器
    feature_matcher = FeatureMatcher(algorithm='SIFT')
    stitcher = ImageStitcher()
    
    print("🔄 开始图像拼接...")
    start_time = time.time()
    
    try:
        result = stitcher.stitch_images(images, feature_matcher)
        stitch_time = time.time() - start_time
        
        if result is not None:
            print(f"✅ 拼接成功!")
            print(f"  处理时间: {stitch_time:.3f}秒")
            print(f"  输入图像: {len(images)}张")
            print(f"  输出尺寸: {result.shape[1]}×{result.shape[0]}像素")
            
            # 保存结果
            output_path = "demo_images/panorama_result.jpg"
            cv2.imwrite(output_path, result)
            print(f"  结果保存: {output_path}")
            
        else:
            print("❌ 拼接失败")
            
    except Exception as e:
        print(f"❌ 拼接过程出错: {str(e)}")


def demo_layout_optimization():
    """演示布局优化功能"""
    print("\n📐 演示布局优化功能")
    print("-" * 40)
    
    # 加载演示图像
    image_paths = create_demo_images()
    processor = ImageProcessor()
    images = [processor.load_image(path) for path in image_paths]
    
    # 创建布局优化器
    optimizer = LayoutOptimizer()
    
    # 测试不同布局方法
    methods = [
        ("grid", "网格布局"),
        ("genetic", "遗传算法优化")
    ]
    
    for method, description in methods:
        print(f"\n🎯 {description}:")
        
        try:
            start_time = time.time()
            result = optimizer.optimize_layout(images, method=method)
            optimization_time = time.time() - start_time
            
            if result is not None:
                print(f"  ✅ 优化成功!")
                print(f"  处理时间: {optimization_time:.3f}秒")
                print(f"  输出尺寸: {result.shape[1]}×{result.shape[0]}像素")
                
                # 保存结果
                output_path = f"demo_images/layout_{method}_demo.jpg"
                cv2.imwrite(output_path, result)
                print(f"  结果保存: {output_path}")
                
            else:
                print(f"  ❌ {description}失败")
                
        except Exception as e:
            print(f"  ❌ {description}出错: {str(e)}")


def demo_image_search():
    """演示图像搜索功能"""
    print("\n🔎 演示图像搜索功能")
    print("-" * 40)
    
    # 加载演示图像
    image_paths = create_demo_images()
    processor = ImageProcessor()
    images = [processor.load_image(path) for path in image_paths]
    
    # 创建搜索器
    searcher = ImageSearcher(feature_type='SIFT')
    
    print("📚 构建图像数据库...")
    
    # 将图像添加到数据库
    for i, (img, path) in enumerate(zip(images[1:], image_paths[1:]), 1):
        image_name = os.path.basename(path)
        searcher.add_to_database(img, f"image_{i}", {'name': image_name, 'path': path})
        print(f"  ➕ 添加: {image_name}")
    
    # 执行搜索
    query_image = images[0]
    query_name = os.path.basename(image_paths[0])
    
    print(f"\n🎯 查询图像: {query_name}")
    
    start_time = time.time()
    similarities = searcher.search_similar(query_image, images[1:])
    search_time = time.time() - start_time
    
    print(f"⏱️  搜索时间: {search_time:.3f}秒")
    print("📊 相似度排序结果:")
    
    for i, similarity in enumerate(similarities):
        image_name = os.path.basename(image_paths[i+1])
        print(f"  {i+1}. {image_name}: {similarity:.3f}")
    
    # 图像聚类
    print("\n🎨 图像聚类分析:")
    labels = searcher.cluster_images(images, n_clusters=2)
    
    for i, (label, path) in enumerate(zip(labels, image_paths)):
        image_name = os.path.basename(path)
        print(f"  {image_name}: 类别 {label}")
    
    # 统计信息
    stats = searcher.get_statistics()
    print(f"\n📈 数据库统计:")
    print(f"  图像总数: {stats['total_images']}")
    print(f"  特征类型: {stats['feature_type']}")


def main():
    """主演示函数"""
    print("🚀 图像拼接与布局优化系统演示")
    print("=" * 50)
    print("本演示将展示系统的核心功能:")
    print("1. 特征匹配算法对比")
    print("2. 图像拼接技术")
    print("3. 布局优化算法")
    print("4. 图像搜索功能")
    print("=" * 50)
    
    try:
        # 运行各个演示
        demo_feature_matching()
        demo_image_stitching()
        demo_layout_optimization()
        demo_image_search()
        
        print("\n🎉 演示完成!")
        print("📁 请查看 demo_images 目录中的结果文件")
        print("\n生成的文件:")
        
        demo_dir = "demo_images"
        if os.path.exists(demo_dir):
            files = os.listdir(demo_dir)
            for file in sorted(files):
                print(f"  📄 {file}")
        
    except Exception as e:
        print(f"\n❌ 演示过程出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
