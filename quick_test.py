#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试图像加载功能
"""

import cv2
import numpy as np
import os
import sys
import glob

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.image_processor import ImageProcessor


def test_image_loading():
    """测试图像加载功能"""
    print("🔍 测试图像加载功能")
    print("=" * 40)
    
    # 创建图像处理器
    processor = ImageProcessor()
    
    # 测试现有图像
    test_dirs = ["test_images", "demo_images"]
    
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            print(f"\n📁 检查目录: {test_dir}")
            
            # 查找图像文件
            image_files = []
            for pattern in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
                image_files.extend(glob.glob(os.path.join(test_dir, pattern)))
            
            if image_files:
                print(f"找到 {len(image_files)} 个图像文件")
                
                for i, file_path in enumerate(image_files[:3]):  # 只测试前3个
                    print(f"\n{i+1}. 测试文件: {os.path.basename(file_path)}")
                    
                    # 检查文件
                    if not os.path.exists(file_path):
                        print("   ❌ 文件不存在")
                        continue
                    
                    file_size = os.path.getsize(file_path)
                    print(f"   文件大小: {file_size} 字节")
                    
                    # 测试OpenCV直接加载
                    cv_image = cv2.imread(file_path)
                    if cv_image is not None:
                        print(f"   ✅ OpenCV加载成功: {cv_image.shape}")
                    else:
                        print("   ❌ OpenCV加载失败")
                        continue
                    
                    # 测试ImageProcessor加载
                    try:
                        proc_image = processor.load_image(file_path)
                        if proc_image is not None:
                            info = processor.get_image_info(proc_image)
                            print(f"   ✅ ImageProcessor加载成功: {info['width']}x{info['height']}")
                        else:
                            print("   ❌ ImageProcessor加载失败")
                    except Exception as e:
                        print(f"   ❌ ImageProcessor出错: {str(e)}")
            else:
                print("   没有找到图像文件")
        else:
            print(f"📁 目录不存在: {test_dir}")


def test_file_formats():
    """测试支持的文件格式"""
    print("\n🎨 测试支持的文件格式")
    print("=" * 40)
    
    processor = ImageProcessor()
    print(f"支持的格式: {processor.supported_formats}")
    
    # 创建测试图像
    test_image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
    
    formats_to_test = ['.jpg', '.png', '.bmp']
    
    for fmt in formats_to_test:
        test_file = f"temp_test{fmt}"
        try:
            # 保存测试图像
            if processor.save_image(test_image, test_file):
                print(f"✅ {fmt} 格式保存成功")
                
                # 尝试加载
                loaded_image = processor.load_image(test_file)
                if loaded_image is not None:
                    print(f"✅ {fmt} 格式加载成功")
                else:
                    print(f"❌ {fmt} 格式加载失败")
                
                # 清理文件
                if os.path.exists(test_file):
                    os.remove(test_file)
            else:
                print(f"❌ {fmt} 格式保存失败")
                
        except Exception as e:
            print(f"❌ {fmt} 格式测试出错: {str(e)}")


def main():
    """主函数"""
    print("🚀 图像加载功能快速测试")
    print("=" * 50)
    
    try:
        test_image_loading()
        test_file_formats()
        
        print("\n✅ 测试完成!")
        
    except Exception as e:
        print(f"\n❌ 测试过程出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
