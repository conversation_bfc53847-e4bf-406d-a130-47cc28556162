#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
立体视觉3D重建模块
基于双目视觉进行深度估计和3D重建
"""

import cv2
import numpy as np
from typing import Tuple, Optional, List
import os


class Stereo3DReconstructor:
    """立体视觉3D重建器"""
    
    def __init__(self):
        """初始化3D重建器"""
        self.camera_matrix_left = None
        self.camera_matrix_right = None
        self.dist_coeffs_left = None
        self.dist_coeffs_right = None
        self.rotation_matrix = None
        self.translation_vector = None
        self.essential_matrix = None
        self.fundamental_matrix = None
        
        # 立体匹配参数
        self.stereo_matcher = None
        self.wls_filter = None
        self._init_stereo_matcher()
    
    def _init_stereo_matcher(self):
        """初始化立体匹配器"""
        # 创建SGBM立体匹配器
        self.stereo_matcher = cv2.StereoSGBM_create(
            minDisparity=0,
            numDisparities=64,  # 必须是16的倍数
            blockSize=11,
            P1=8 * 3 * 11**2,
            P2=32 * 3 * 11**2,
            disp12MaxDiff=1,
            uniquenessRatio=15,
            speckleWindowSize=0,
            speckleRange=2,
            preFilterCap=63,
            mode=cv2.STEREO_SGBM_MODE_SGBM_3WAY
        )
        
        # 创建WLS滤波器用于后处理
        try:
            self.wls_filter = cv2.ximgproc.createDisparityWLSFilter(self.stereo_matcher)
            self.wls_filter.setLambda(80000)
            self.wls_filter.setSigmaColor(1.2)
        except AttributeError:
            print("WLS滤波器不可用，使用基础立体匹配")
            self.wls_filter = None
    
    def calibrate_stereo_cameras(self, left_images, right_images, chessboard_size=(9, 6)):
        """
        标定立体相机
        
        Args:
            left_images (list): 左相机图像列表
            right_images (list): 右相机图像列表
            chessboard_size (tuple): 棋盘格尺寸
            
        Returns:
            bool: 标定是否成功
        """
        try:
            # 准备棋盘格角点
            criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001)
            
            # 生成棋盘格世界坐标
            objp = np.zeros((chessboard_size[0] * chessboard_size[1], 3), np.float32)
            objp[:, :2] = np.mgrid[0:chessboard_size[0], 0:chessboard_size[1]].T.reshape(-1, 2)
            
            # 存储角点
            objpoints = []  # 3D点
            imgpoints_left = []  # 左图像2D点
            imgpoints_right = []  # 右图像2D点
            
            for left_img, right_img in zip(left_images, right_images):
                # 转换为灰度图
                gray_left = cv2.cvtColor(left_img, cv2.COLOR_BGR2GRAY)
                gray_right = cv2.cvtColor(right_img, cv2.COLOR_BGR2GRAY)
                
                # 查找棋盘格角点
                ret_left, corners_left = cv2.findChessboardCorners(gray_left, chessboard_size, None)
                ret_right, corners_right = cv2.findChessboardCorners(gray_right, chessboard_size, None)
                
                if ret_left and ret_right:
                    objpoints.append(objp)
                    
                    # 精确化角点位置
                    corners_left = cv2.cornerSubPix(gray_left, corners_left, (11, 11), (-1, -1), criteria)
                    corners_right = cv2.cornerSubPix(gray_right, corners_right, (11, 11), (-1, -1), criteria)
                    
                    imgpoints_left.append(corners_left)
                    imgpoints_right.append(corners_right)
            
            if len(objpoints) < 10:
                print(f"标定图像不足: {len(objpoints)}")
                return False
            
            # 获取图像尺寸
            img_shape = gray_left.shape[::-1]
            
            # 单独标定左右相机
            ret_left, mtx_left, dist_left, rvecs_left, tvecs_left = cv2.calibrateCamera(
                objpoints, imgpoints_left, img_shape, None, None
            )
            
            ret_right, mtx_right, dist_right, rvecs_right, tvecs_right = cv2.calibrateCamera(
                objpoints, imgpoints_right, img_shape, None, None
            )
            
            # 立体标定
            ret, mtx_left, dist_left, mtx_right, dist_right, R, T, E, F = cv2.stereoCalibrate(
                objpoints, imgpoints_left, imgpoints_right,
                mtx_left, dist_left, mtx_right, dist_right,
                img_shape,
                criteria=criteria,
                flags=cv2.CALIB_FIX_INTRINSIC
            )
            
            # 保存标定结果
            self.camera_matrix_left = mtx_left
            self.camera_matrix_right = mtx_right
            self.dist_coeffs_left = dist_left
            self.dist_coeffs_right = dist_right
            self.rotation_matrix = R
            self.translation_vector = T
            self.essential_matrix = E
            self.fundamental_matrix = F
            
            print(f"立体标定完成，重投影误差: {ret:.4f}")
            return True
            
        except Exception as e:
            print(f"立体标定失败: {str(e)}")
            return False
    
    def rectify_stereo_images(self, left_image, right_image):
        """
        立体图像校正
        
        Args:
            left_image (numpy.ndarray): 左图像
            right_image (numpy.ndarray): 右图像
            
        Returns:
            tuple: 校正后的左右图像
        """
        if self.camera_matrix_left is None:
            print("相机未标定")
            return left_image, right_image
        
        try:
            img_shape = left_image.shape[:2][::-1]
            
            # 立体校正
            R1, R2, P1, P2, Q, validPixROI1, validPixROI2 = cv2.stereoRectify(
                self.camera_matrix_left, self.dist_coeffs_left,
                self.camera_matrix_right, self.dist_coeffs_right,
                img_shape, self.rotation_matrix, self.translation_vector
            )
            
            # 计算校正映射
            map1_left, map2_left = cv2.initUndistortRectifyMap(
                self.camera_matrix_left, self.dist_coeffs_left, R1, P1, img_shape, cv2.CV_16SC2
            )
            
            map1_right, map2_right = cv2.initUndistortRectifyMap(
                self.camera_matrix_right, self.dist_coeffs_right, R2, P2, img_shape, cv2.CV_16SC2
            )
            
            # 应用校正
            rectified_left = cv2.remap(left_image, map1_left, map2_left, cv2.INTER_LINEAR)
            rectified_right = cv2.remap(right_image, map1_right, map2_right, cv2.INTER_LINEAR)
            
            return rectified_left, rectified_right
            
        except Exception as e:
            print(f"立体校正失败: {str(e)}")
            return left_image, right_image
    
    def compute_disparity(self, left_image, right_image):
        """
        计算视差图
        
        Args:
            left_image (numpy.ndarray): 左图像
            right_image (numpy.ndarray): 右图像
            
        Returns:
            numpy.ndarray: 视差图
        """
        try:
            # 转换为灰度图
            if len(left_image.shape) == 3:
                gray_left = cv2.cvtColor(left_image, cv2.COLOR_BGR2GRAY)
                gray_right = cv2.cvtColor(right_image, cv2.COLOR_BGR2GRAY)
            else:
                gray_left = left_image
                gray_right = right_image
            
            # 计算视差
            disparity = self.stereo_matcher.compute(gray_left, gray_right)
            
            # 应用WLS滤波器（如果可用）
            if self.wls_filter is not None:
                # 创建右视差图
                right_matcher = cv2.ximgproc.createRightMatcher(self.stereo_matcher)
                disparity_right = right_matcher.compute(gray_right, gray_left)
                
                # 应用滤波
                disparity = self.wls_filter.filter(disparity, gray_left, None, disparity_right)
            
            # 归一化视差图
            disparity_normalized = cv2.normalize(disparity, None, 0, 255, cv2.NORM_MINMAX, cv2.CV_8U)
            
            return disparity_normalized
            
        except Exception as e:
            print(f"视差计算失败: {str(e)}")
            return None
    
    def disparity_to_depth(self, disparity, baseline=0.1, focal_length=None):
        """
        将视差转换为深度
        
        Args:
            disparity (numpy.ndarray): 视差图
            baseline (float): 基线距离（米）
            focal_length (float): 焦距（像素）
            
        Returns:
            numpy.ndarray: 深度图
        """
        if focal_length is None and self.camera_matrix_left is not None:
            focal_length = self.camera_matrix_left[0, 0]
        elif focal_length is None:
            focal_length = 500  # 默认焦距
        
        # 避免除零
        disparity_float = disparity.astype(np.float32)
        disparity_float[disparity_float <= 0] = 0.1
        
        # 计算深度 Z = (baseline * focal_length) / disparity
        depth = (baseline * focal_length) / disparity_float
        
        # 限制深度范围
        depth = np.clip(depth, 0, 10)  # 最大10米
        
        return depth
    
    def generate_point_cloud(self, left_image, disparity, baseline=0.1):
        """
        生成点云
        
        Args:
            left_image (numpy.ndarray): 左图像
            disparity (numpy.ndarray): 视差图
            baseline (float): 基线距离
            
        Returns:
            numpy.ndarray: 点云数据 (N, 6) [x, y, z, r, g, b]
        """
        try:
            if self.camera_matrix_left is None:
                print("相机未标定，使用默认参数")
                focal_length = 500
                cx, cy = left_image.shape[1] // 2, left_image.shape[0] // 2
            else:
                focal_length = self.camera_matrix_left[0, 0]
                cx, cy = self.camera_matrix_left[0, 2], self.camera_matrix_left[1, 2]
            
            # 获取深度
            depth = self.disparity_to_depth(disparity, baseline, focal_length)
            
            # 创建坐标网格
            h, w = depth.shape
            x, y = np.meshgrid(np.arange(w), np.arange(h))
            
            # 计算3D坐标
            x_3d = (x - cx) * depth / focal_length
            y_3d = (y - cy) * depth / focal_length
            z_3d = depth
            
            # 获取颜色信息
            if len(left_image.shape) == 3:
                colors = left_image
            else:
                colors = cv2.cvtColor(left_image, cv2.COLOR_GRAY2BGR)
            
            # 过滤有效点
            valid_mask = (disparity > 0) & (depth < 10) & (depth > 0.1)
            
            # 构建点云
            points_3d = np.stack([x_3d[valid_mask], y_3d[valid_mask], z_3d[valid_mask]], axis=1)
            colors_valid = colors[valid_mask]
            
            # 合并坐标和颜色
            point_cloud = np.hstack([points_3d, colors_valid])
            
            return point_cloud
            
        except Exception as e:
            print(f"点云生成失败: {str(e)}")
            return None
    
    def save_point_cloud_ply(self, point_cloud, filepath):
        """
        保存点云为PLY格式
        
        Args:
            point_cloud (numpy.ndarray): 点云数据
            filepath (str): 输出文件路径
            
        Returns:
            bool: 保存是否成功
        """
        try:
            if point_cloud is None or len(point_cloud) == 0:
                print("点云数据为空")
                return False
            
            with open(filepath, 'w') as f:
                # 写入PLY头部
                f.write("ply\n")
                f.write("format ascii 1.0\n")
                f.write(f"element vertex {len(point_cloud)}\n")
                f.write("property float x\n")
                f.write("property float y\n")
                f.write("property float z\n")
                f.write("property uchar red\n")
                f.write("property uchar green\n")
                f.write("property uchar blue\n")
                f.write("end_header\n")
                
                # 写入点云数据
                for point in point_cloud:
                    x, y, z, b, g, r = point  # OpenCV使用BGR格式
                    f.write(f"{x:.6f} {y:.6f} {z:.6f} {int(r)} {int(g)} {int(b)}\n")
            
            print(f"点云已保存: {filepath}")
            return True
            
        except Exception as e:
            print(f"保存点云失败: {str(e)}")
            return False
    
    def create_depth_map_visualization(self, depth):
        """
        创建深度图可视化
        
        Args:
            depth (numpy.ndarray): 深度图
            
        Returns:
            numpy.ndarray: 彩色深度图
        """
        # 归一化深度值
        depth_normalized = cv2.normalize(depth, None, 0, 255, cv2.NORM_MINMAX, cv2.CV_8U)
        
        # 应用颜色映射
        depth_colored = cv2.applyColorMap(depth_normalized, cv2.COLORMAP_JET)
        
        return depth_colored
    
    def estimate_pose_from_stereo(self, left_image, right_image, previous_pose=None):
        """
        从立体图像估计相机位姿
        
        Args:
            left_image (numpy.ndarray): 左图像
            right_image (numpy.ndarray): 右图像
            previous_pose (numpy.ndarray): 前一帧位姿
            
        Returns:
            tuple: (旋转矩阵, 平移向量)
        """
        try:
            # 检测特征点
            detector = cv2.ORB_create(nfeatures=1000)
            
            gray_left = cv2.cvtColor(left_image, cv2.COLOR_BGR2GRAY)
            gray_right = cv2.cvtColor(right_image, cv2.COLOR_BGR2GRAY)
            
            kp_left, desc_left = detector.detectAndCompute(gray_left, None)
            kp_right, desc_right = detector.detectAndCompute(gray_right, None)
            
            if desc_left is None or desc_right is None:
                return None, None
            
            # 匹配特征点
            matcher = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)
            matches = matcher.match(desc_left, desc_right)
            matches = sorted(matches, key=lambda x: x.distance)
            
            if len(matches) < 50:
                print("特征匹配点不足")
                return None, None
            
            # 提取匹配点坐标
            pts_left = np.float32([kp_left[m.queryIdx].pt for m in matches]).reshape(-1, 1, 2)
            pts_right = np.float32([kp_right[m.trainIdx].pt for m in matches]).reshape(-1, 1, 2)
            
            # 计算本质矩阵
            if self.camera_matrix_left is not None:
                E, mask = cv2.findEssentialMat(
                    pts_left, pts_right, 
                    self.camera_matrix_left, 
                    method=cv2.RANSAC, 
                    prob=0.999, 
                    threshold=1.0
                )
                
                # 恢复位姿
                _, R, t, mask = cv2.recoverPose(E, pts_left, pts_right, self.camera_matrix_left)
                
                return R, t
            else:
                print("相机未标定")
                return None, None
                
        except Exception as e:
            print(f"位姿估计失败: {str(e)}")
            return None, None
    
    def save_calibration(self, filepath):
        """保存标定参数"""
        try:
            calibration_data = {
                'camera_matrix_left': self.camera_matrix_left,
                'camera_matrix_right': self.camera_matrix_right,
                'dist_coeffs_left': self.dist_coeffs_left,
                'dist_coeffs_right': self.dist_coeffs_right,
                'rotation_matrix': self.rotation_matrix,
                'translation_vector': self.translation_vector,
                'essential_matrix': self.essential_matrix,
                'fundamental_matrix': self.fundamental_matrix
            }
            
            np.savez(filepath, **calibration_data)
            print(f"标定参数已保存: {filepath}")
            return True
            
        except Exception as e:
            print(f"保存标定参数失败: {str(e)}")
            return False
    
    def load_calibration(self, filepath):
        """加载标定参数"""
        try:
            if not os.path.exists(filepath):
                print(f"标定文件不存在: {filepath}")
                return False
            
            data = np.load(filepath)
            
            self.camera_matrix_left = data['camera_matrix_left']
            self.camera_matrix_right = data['camera_matrix_right']
            self.dist_coeffs_left = data['dist_coeffs_left']
            self.dist_coeffs_right = data['dist_coeffs_right']
            self.rotation_matrix = data['rotation_matrix']
            self.translation_vector = data['translation_vector']
            self.essential_matrix = data['essential_matrix']
            self.fundamental_matrix = data['fundamental_matrix']
            
            print(f"标定参数已加载: {filepath}")
            return True
            
        except Exception as e:
            print(f"加载标定参数失败: {str(e)}")
            return False
