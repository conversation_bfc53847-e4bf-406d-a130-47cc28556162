#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能图像分析模块
包含目标检测、语义分割、图像质量评估等高级功能
"""

import cv2
import numpy as np
from typing import List, Tuple, Dict, Optional
import os
import json


class IntelligentImageAnalyzer:
    """智能图像分析器"""
    
    def __init__(self):
        """初始化智能分析器"""
        self.object_detector = None
        self.face_detector = None
        self.edge_detector = None
        self._init_detectors()
    
    def _init_detectors(self):
        """初始化检测器"""
        try:
            # 初始化人脸检测器
            self.face_detector = cv2.CascadeClassifier(
                cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
            )
            
            # 初始化边缘检测器
            self.edge_detector = cv2.Canny
            
            print("智能分析器初始化完成")
            
        except Exception as e:
            print(f"检测器初始化失败: {str(e)}")
    
    def analyze_image_quality(self, image):
        """
        分析图像质量
        
        Args:
            image (numpy.ndarray): 输入图像
            
        Returns:
            dict: 质量分析结果
        """
        try:
            quality_metrics = {}
            
            # 转换为灰度图
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image
            
            # 1. 清晰度评估（基于拉普拉斯算子）
            laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
            quality_metrics['sharpness'] = float(laplacian_var)
            
            # 2. 对比度评估
            contrast = gray.std()
            quality_metrics['contrast'] = float(contrast)
            
            # 3. 亮度评估
            brightness = gray.mean()
            quality_metrics['brightness'] = float(brightness)
            
            # 4. 噪声评估（基于高频成分）
            noise_level = self._estimate_noise_level(gray)
            quality_metrics['noise_level'] = float(noise_level)
            
            # 5. 色彩丰富度（如果是彩色图像）
            if len(image.shape) == 3:
                color_richness = self._calculate_color_richness(image)
                quality_metrics['color_richness'] = float(color_richness)
            
            # 6. 整体质量评分
            overall_score = self._calculate_overall_quality_score(quality_metrics)
            quality_metrics['overall_score'] = float(overall_score)
            
            # 7. 质量等级
            quality_metrics['quality_level'] = self._get_quality_level(overall_score)
            
            return quality_metrics
            
        except Exception as e:
            print(f"图像质量分析失败: {str(e)}")
            return {}
    
    def _estimate_noise_level(self, gray_image):
        """估计图像噪声水平"""
        # 使用高斯滤波后的差值估计噪声
        blurred = cv2.GaussianBlur(gray_image, (5, 5), 0)
        noise = gray_image.astype(np.float32) - blurred.astype(np.float32)
        return np.std(noise)
    
    def _calculate_color_richness(self, color_image):
        """计算色彩丰富度"""
        # 转换到HSV空间
        hsv = cv2.cvtColor(color_image, cv2.COLOR_BGR2HSV)
        
        # 计算饱和度的标准差
        saturation = hsv[:, :, 1]
        color_richness = np.std(saturation)
        
        return color_richness
    
    def _calculate_overall_quality_score(self, metrics):
        """计算整体质量评分"""
        # 归一化各项指标
        sharpness_score = min(metrics.get('sharpness', 0) / 1000, 1.0)
        contrast_score = min(metrics.get('contrast', 0) / 100, 1.0)
        brightness_score = 1.0 - abs(metrics.get('brightness', 128) - 128) / 128
        noise_score = max(0, 1.0 - metrics.get('noise_level', 0) / 50)
        
        # 加权平均
        overall_score = (
            0.3 * sharpness_score +
            0.25 * contrast_score +
            0.2 * brightness_score +
            0.25 * noise_score
        )
        
        return overall_score * 100  # 转换为0-100分
    
    def _get_quality_level(self, score):
        """根据评分获取质量等级"""
        if score >= 80:
            return "优秀"
        elif score >= 60:
            return "良好"
        elif score >= 40:
            return "一般"
        else:
            return "较差"
    
    def detect_objects(self, image):
        """
        目标检测（简化版）
        
        Args:
            image (numpy.ndarray): 输入图像
            
        Returns:
            list: 检测到的目标列表
        """
        try:
            objects = []
            
            # 人脸检测
            faces = self.detect_faces(image)
            for face in faces:
                objects.append({
                    'type': 'face',
                    'bbox': face,
                    'confidence': 0.8
                })
            
            # 边缘检测（作为物体轮廓的简化检测）
            contours = self.detect_contours(image)
            for i, contour in enumerate(contours[:10]):  # 限制数量
                x, y, w, h = cv2.boundingRect(contour)
                if w > 50 and h > 50:  # 过滤小物体
                    objects.append({
                        'type': 'object',
                        'bbox': (x, y, w, h),
                        'confidence': 0.6,
                        'area': cv2.contourArea(contour)
                    })
            
            return objects
            
        except Exception as e:
            print(f"目标检测失败: {str(e)}")
            return []
    
    def detect_faces(self, image):
        """
        人脸检测
        
        Args:
            image (numpy.ndarray): 输入图像
            
        Returns:
            list: 人脸边界框列表
        """
        try:
            if self.face_detector is None:
                return []
            
            # 转换为灰度图
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image
            
            # 检测人脸
            faces = self.face_detector.detectMultiScale(
                gray,
                scaleFactor=1.1,
                minNeighbors=5,
                minSize=(30, 30)
            )
            
            return faces.tolist()
            
        except Exception as e:
            print(f"人脸检测失败: {str(e)}")
            return []
    
    def detect_contours(self, image):
        """
        轮廓检测
        
        Args:
            image (numpy.ndarray): 输入图像
            
        Returns:
            list: 轮廓列表
        """
        try:
            # 转换为灰度图
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image
            
            # 边缘检测
            edges = cv2.Canny(gray, 50, 150)
            
            # 查找轮廓
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 按面积排序
            contours = sorted(contours, key=cv2.contourArea, reverse=True)
            
            return contours
            
        except Exception as e:
            print(f"轮廓检测失败: {str(e)}")
            return []
    
    def analyze_composition(self, image):
        """
        分析图像构图
        
        Args:
            image (numpy.ndarray): 输入图像
            
        Returns:
            dict: 构图分析结果
        """
        try:
            composition_analysis = {}
            
            h, w = image.shape[:2]
            
            # 1. 三分法则分析
            rule_of_thirds = self._analyze_rule_of_thirds(image)
            composition_analysis['rule_of_thirds'] = rule_of_thirds
            
            # 2. 对称性分析
            symmetry = self._analyze_symmetry(image)
            composition_analysis['symmetry'] = symmetry
            
            # 3. 主体位置分析
            subject_position = self._analyze_subject_position(image)
            composition_analysis['subject_position'] = subject_position
            
            # 4. 色彩平衡分析
            color_balance = self._analyze_color_balance(image)
            composition_analysis['color_balance'] = color_balance
            
            # 5. 整体构图评分
            composition_score = self._calculate_composition_score(composition_analysis)
            composition_analysis['composition_score'] = composition_score
            
            return composition_analysis
            
        except Exception as e:
            print(f"构图分析失败: {str(e)}")
            return {}
    
    def _analyze_rule_of_thirds(self, image):
        """分析三分法则"""
        h, w = image.shape[:2]
        
        # 三分线位置
        third_h = h // 3
        third_w = w // 3
        
        # 转换为灰度图
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        # 计算三分线附近的兴趣点密度
        interest_points = cv2.goodFeaturesToTrack(gray, maxCorners=100, qualityLevel=0.01, minDistance=10)
        
        if interest_points is None:
            return {'score': 0, 'points_on_lines': 0}
        
        points_on_lines = 0
        tolerance = 20  # 像素容差
        
        for point in interest_points:
            x, y = point.ravel()
            
            # 检查是否在三分线附近
            if (abs(x - third_w) < tolerance or abs(x - 2*third_w) < tolerance or
                abs(y - third_h) < tolerance or abs(y - 2*third_h) < tolerance):
                points_on_lines += 1
        
        score = points_on_lines / len(interest_points) if len(interest_points) > 0 else 0
        
        return {
            'score': float(score),
            'points_on_lines': int(points_on_lines),
            'total_points': len(interest_points)
        }
    
    def _analyze_symmetry(self, image):
        """分析对称性"""
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        h, w = gray.shape
        
        # 水平对称性
        left_half = gray[:, :w//2]
        right_half = cv2.flip(gray[:, w//2:], 1)
        
        # 调整尺寸以匹配
        min_width = min(left_half.shape[1], right_half.shape[1])
        left_half = left_half[:, :min_width]
        right_half = right_half[:, :min_width]
        
        horizontal_symmetry = cv2.matchTemplate(left_half, right_half, cv2.TM_CCOEFF_NORMED)[0, 0]
        
        # 垂直对称性
        top_half = gray[:h//2, :]
        bottom_half = cv2.flip(gray[h//2:, :], 0)
        
        min_height = min(top_half.shape[0], bottom_half.shape[0])
        top_half = top_half[:min_height, :]
        bottom_half = bottom_half[:min_height, :]
        
        vertical_symmetry = cv2.matchTemplate(top_half, bottom_half, cv2.TM_CCOEFF_NORMED)[0, 0]
        
        return {
            'horizontal_symmetry': float(horizontal_symmetry),
            'vertical_symmetry': float(vertical_symmetry),
            'overall_symmetry': float((horizontal_symmetry + vertical_symmetry) / 2)
        }
    
    def _analyze_subject_position(self, image):
        """分析主体位置"""
        # 检测显著性区域
        saliency = cv2.saliency.StaticSaliencySpectralResidual_create()
        success, saliency_map = saliency.computeSaliency(image)
        
        if not success:
            return {'center_bias': 0, 'main_subject_position': 'unknown'}
        
        # 找到最显著的区域
        _, thresh = cv2.threshold((saliency_map * 255).astype(np.uint8), 127, 255, cv2.THRESH_BINARY)
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return {'center_bias': 0, 'main_subject_position': 'unknown'}
        
        # 找到最大的显著区域
        largest_contour = max(contours, key=cv2.contourArea)
        M = cv2.moments(largest_contour)
        
        if M['m00'] == 0:
            return {'center_bias': 0, 'main_subject_position': 'unknown'}
        
        # 计算质心
        cx = int(M['m10'] / M['m00'])
        cy = int(M['m01'] / M['m00'])
        
        h, w = image.shape[:2]
        center_x, center_y = w // 2, h // 2
        
        # 计算与中心的距离
        distance_from_center = np.sqrt((cx - center_x)**2 + (cy - center_y)**2)
        max_distance = np.sqrt(center_x**2 + center_y**2)
        center_bias = 1 - (distance_from_center / max_distance)
        
        # 确定主体位置
        if cx < w // 3:
            h_pos = 'left'
        elif cx > 2 * w // 3:
            h_pos = 'right'
        else:
            h_pos = 'center'
        
        if cy < h // 3:
            v_pos = 'top'
        elif cy > 2 * h // 3:
            v_pos = 'bottom'
        else:
            v_pos = 'middle'
        
        position = f"{v_pos}_{h_pos}"
        
        return {
            'center_bias': float(center_bias),
            'main_subject_position': position,
            'subject_coordinates': (int(cx), int(cy))
        }
    
    def _analyze_color_balance(self, image):
        """分析色彩平衡"""
        if len(image.shape) != 3:
            return {'balance_score': 0, 'dominant_colors': []}
        
        # 计算各通道的均值
        b_mean = np.mean(image[:, :, 0])
        g_mean = np.mean(image[:, :, 1])
        r_mean = np.mean(image[:, :, 2])
        
        # 计算色彩平衡度
        total_mean = (b_mean + g_mean + r_mean) / 3
        balance_score = 1 - (np.std([b_mean, g_mean, r_mean]) / total_mean)
        
        # 找到主导色彩
        dominant_colors = self._find_dominant_colors(image)
        
        return {
            'balance_score': float(balance_score),
            'channel_means': {'blue': float(b_mean), 'green': float(g_mean), 'red': float(r_mean)},
            'dominant_colors': dominant_colors
        }
    
    def _find_dominant_colors(self, image, k=5):
        """找到主导色彩"""
        # 重塑图像为像素列表
        pixels = image.reshape(-1, 3)
        
        # 使用K-means聚类找到主导色彩
        criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 20, 1.0)
        _, labels, centers = cv2.kmeans(pixels.astype(np.float32), k, None, criteria, 10, cv2.KMEANS_RANDOM_CENTERS)
        
        # 计算每个颜色的比例
        unique, counts = np.unique(labels, return_counts=True)
        percentages = counts / len(labels)
        
        # 排序并返回主导色彩
        dominant_colors = []
        for i in np.argsort(percentages)[::-1]:
            color = centers[i].astype(int)
            percentage = percentages[i]
            dominant_colors.append({
                'color': [int(color[2]), int(color[1]), int(color[0])],  # BGR to RGB
                'percentage': float(percentage)
            })
        
        return dominant_colors
    
    def _calculate_composition_score(self, analysis):
        """计算构图评分"""
        rule_of_thirds_score = analysis.get('rule_of_thirds', {}).get('score', 0)
        symmetry_score = analysis.get('symmetry', {}).get('overall_symmetry', 0)
        center_bias = analysis.get('subject_position', {}).get('center_bias', 0)
        balance_score = analysis.get('color_balance', {}).get('balance_score', 0)
        
        # 加权平均
        composition_score = (
            0.3 * rule_of_thirds_score +
            0.2 * max(0, symmetry_score) +
            0.3 * center_bias +
            0.2 * balance_score
        )
        
        return float(composition_score * 100)
    
    def generate_analysis_report(self, image, save_path=None):
        """
        生成完整的图像分析报告
        
        Args:
            image (numpy.ndarray): 输入图像
            save_path (str): 报告保存路径
            
        Returns:
            dict: 完整分析报告
        """
        try:
            report = {
                'image_info': {
                    'shape': image.shape,
                    'dtype': str(image.dtype),
                    'size_mb': image.nbytes / (1024 * 1024)
                },
                'quality_analysis': self.analyze_image_quality(image),
                'object_detection': self.detect_objects(image),
                'composition_analysis': self.analyze_composition(image),
                'timestamp': time.time()
            }
            
            # 保存报告
            if save_path:
                with open(save_path, 'w', encoding='utf-8') as f:
                    json.dump(report, f, indent=2, ensure_ascii=False)
                print(f"分析报告已保存: {save_path}")
            
            return report
            
        except Exception as e:
            print(f"生成分析报告失败: {str(e)}")
            return {}


# 导入time模块
import time
