#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时处理模块
支持摄像头实时图像拼接和处理
"""

import cv2
import numpy as np
import threading
import time
from queue import Queue
from typing import List, Optional, Callable
import os


class RealTimeProcessor:
    """实时图像处理器"""
    
    def __init__(self, feature_matcher=None):
        """
        初始化实时处理器
        
        Args:
            feature_matcher: 特征匹配器实例
        """
        self.feature_matcher = feature_matcher
        self.cameras = []
        self.capture_threads = []
        self.processing_thread = None
        self.is_running = False
        
        # 图像队列
        self.frame_queues = []
        self.result_queue = Queue(maxsize=10)
        
        # 处理参数
        self.frame_skip = 2  # 跳帧处理
        self.max_fps = 15    # 最大帧率
        self.frame_count = 0
        
        # 缓存
        self.last_result = None
        self.processing_time = 0
        
        # 回调函数
        self.result_callback = None
        self.error_callback = None
    
    def add_camera(self, camera_id=0):
        """
        添加摄像头
        
        Args:
            camera_id (int): 摄像头ID
            
        Returns:
            bool: 是否成功添加
        """
        try:
            cap = cv2.VideoCapture(camera_id)
            if not cap.isOpened():
                print(f"无法打开摄像头 {camera_id}")
                return False
            
            # 设置摄像头参数
            cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            cap.set(cv2.CAP_PROP_FPS, 30)
            
            self.cameras.append(cap)
            self.frame_queues.append(Queue(maxsize=5))
            
            print(f"成功添加摄像头 {camera_id}")
            return True
            
        except Exception as e:
            print(f"添加摄像头失败: {str(e)}")
            return False
    
    def start_capture(self):
        """开始捕获"""
        if self.is_running:
            print("实时处理已在运行")
            return
        
        if not self.cameras:
            print("没有可用的摄像头")
            return
        
        self.is_running = True
        
        # 启动捕获线程
        for i, camera in enumerate(self.cameras):
            thread = threading.Thread(
                target=self._capture_worker,
                args=(camera, self.frame_queues[i], i),
                daemon=True
            )
            thread.start()
            self.capture_threads.append(thread)
        
        # 启动处理线程
        self.processing_thread = threading.Thread(
            target=self._processing_worker,
            daemon=True
        )
        self.processing_thread.start()
        
        print(f"开始实时处理，使用 {len(self.cameras)} 个摄像头")
    
    def stop_capture(self):
        """停止捕获"""
        self.is_running = False
        
        # 等待线程结束
        for thread in self.capture_threads:
            if thread.is_alive():
                thread.join(timeout=1.0)
        
        if self.processing_thread and self.processing_thread.is_alive():
            self.processing_thread.join(timeout=1.0)
        
        # 清空队列
        for queue in self.frame_queues:
            while not queue.empty():
                try:
                    queue.get_nowait()
                except:
                    break
        
        while not self.result_queue.empty():
            try:
                self.result_queue.get_nowait()
            except:
                break
        
        self.capture_threads.clear()
        print("实时处理已停止")
    
    def _capture_worker(self, camera, frame_queue, camera_id):
        """摄像头捕获工作线程"""
        frame_interval = 1.0 / self.max_fps
        last_time = time.time()
        
        while self.is_running:
            try:
                ret, frame = camera.read()
                if not ret:
                    print(f"摄像头 {camera_id} 读取失败")
                    break
                
                current_time = time.time()
                if current_time - last_time < frame_interval:
                    continue
                
                # 预处理帧
                processed_frame = self._preprocess_frame(frame)
                
                # 添加到队列
                if not frame_queue.full():
                    frame_queue.put((processed_frame, current_time))
                else:
                    # 队列满时，移除旧帧
                    try:
                        frame_queue.get_nowait()
                        frame_queue.put((processed_frame, current_time))
                    except:
                        pass
                
                last_time = current_time
                
            except Exception as e:
                print(f"摄像头 {camera_id} 捕获错误: {str(e)}")
                if self.error_callback:
                    self.error_callback(f"摄像头 {camera_id} 错误: {str(e)}")
                break
    
    def _processing_worker(self):
        """图像处理工作线程"""
        while self.is_running:
            try:
                # 收集当前帧
                frames = []
                timestamps = []
                
                for i, queue in enumerate(self.frame_queues):
                    if not queue.empty():
                        frame, timestamp = queue.get()
                        frames.append(frame)
                        timestamps.append(timestamp)
                
                if len(frames) < 2:
                    time.sleep(0.01)  # 等待更多帧
                    continue
                
                # 跳帧处理
                self.frame_count += 1
                if self.frame_count % self.frame_skip != 0:
                    continue
                
                # 处理帧
                start_time = time.time()
                result = self._process_frames(frames)
                self.processing_time = time.time() - start_time
                
                if result is not None:
                    self.last_result = result
                    
                    # 添加到结果队列
                    if not self.result_queue.full():
                        self.result_queue.put((result, time.time()))
                    
                    # 调用回调函数
                    if self.result_callback:
                        self.result_callback(result)
                
            except Exception as e:
                print(f"处理错误: {str(e)}")
                if self.error_callback:
                    self.error_callback(f"处理错误: {str(e)}")
                time.sleep(0.1)
    
    def _preprocess_frame(self, frame):
        """预处理帧"""
        # 调整大小
        height, width = frame.shape[:2]
        if width > 640:
            scale = 640 / width
            new_width = 640
            new_height = int(height * scale)
            frame = cv2.resize(frame, (new_width, new_height))
        
        # 降噪
        frame = cv2.bilateralFilter(frame, 5, 50, 50)
        
        return frame
    
    def _process_frames(self, frames):
        """处理多个帧"""
        if len(frames) < 2:
            return None
        
        try:
            # 快速拼接模式
            if len(frames) == 2:
                return self._quick_stitch_two_frames(frames[0], frames[1])
            else:
                return self._stitch_multiple_frames(frames)
                
        except Exception as e:
            print(f"帧处理失败: {str(e)}")
            return None
    
    def _quick_stitch_two_frames(self, frame1, frame2):
        """快速拼接两帧"""
        if self.feature_matcher is None:
            # 简单水平拼接
            return np.hstack([frame1, frame2])
        
        try:
            # 检测特征点
            kp1, desc1 = self.feature_matcher.detect_and_compute(frame1)
            kp2, desc2 = self.feature_matcher.detect_and_compute(frame2)
            
            if desc1 is None or desc2 is None:
                return np.hstack([frame1, frame2])
            
            # 匹配特征点
            matches = self.feature_matcher.match_features(desc1, desc2)
            
            if len(matches) < 10:
                return np.hstack([frame1, frame2])
            
            # 计算单应性矩阵
            homography, mask = self.feature_matcher.find_homography(kp1, kp2, matches)
            
            if homography is None:
                return np.hstack([frame1, frame2])
            
            # 快速变换和拼接
            h1, w1 = frame1.shape[:2]
            h2, w2 = frame2.shape[:2]
            
            # 计算输出尺寸
            corners2 = np.float32([[0, 0], [w2, 0], [w2, h2], [0, h2]]).reshape(-1, 1, 2)
            corners2_transformed = cv2.perspectiveTransform(corners2, homography)
            
            all_corners = np.concatenate([
                np.float32([[0, 0], [w1, 0], [w1, h1], [0, h1]]).reshape(-1, 1, 2),
                corners2_transformed
            ], axis=0)
            
            [x_min, y_min] = np.int32(all_corners.min(axis=0).ravel() - 0.5)
            [x_max, y_max] = np.int32(all_corners.max(axis=0).ravel() + 0.5)
            
            translation = np.array([[1, 0, -x_min], [0, 1, -y_min], [0, 0, 1]])
            homography_translated = translation.dot(homography)
            
            output_width = x_max - x_min
            output_height = y_max - y_min
            
            # 变换第二帧
            warped_frame2 = cv2.warpPerspective(
                frame2, homography_translated, 
                (output_width, output_height)
            )
            
            # 创建结果图像
            result = np.zeros((output_height, output_width, 3), dtype=np.uint8)
            result[-y_min:-y_min+h1, -x_min:-x_min+w1] = frame1
            
            # 简单融合
            mask = warped_frame2 > 0
            result[mask] = warped_frame2[mask]
            
            return result
            
        except Exception as e:
            print(f"快速拼接失败: {str(e)}")
            return np.hstack([frame1, frame2])
    
    def _stitch_multiple_frames(self, frames):
        """拼接多个帧"""
        if len(frames) < 2:
            return None
        
        # 逐步拼接
        result = frames[0]
        for i in range(1, len(frames)):
            result = self._quick_stitch_two_frames(result, frames[i])
            if result is None:
                break
        
        return result
    
    def get_latest_result(self):
        """获取最新结果"""
        if not self.result_queue.empty():
            try:
                return self.result_queue.get_nowait()
            except:
                pass
        return self.last_result, time.time()
    
    def set_result_callback(self, callback):
        """设置结果回调函数"""
        self.result_callback = callback
    
    def set_error_callback(self, callback):
        """设置错误回调函数"""
        self.error_callback = callback
    
    def get_statistics(self):
        """获取统计信息"""
        return {
            'camera_count': len(self.cameras),
            'is_running': self.is_running,
            'processing_time': self.processing_time,
            'frame_count': self.frame_count,
            'queue_sizes': [q.qsize() for q in self.frame_queues],
            'result_queue_size': self.result_queue.qsize()
        }
    
    def save_current_result(self, filepath):
        """保存当前结果"""
        if self.last_result is not None:
            try:
                cv2.imwrite(filepath, self.last_result)
                return True
            except Exception as e:
                print(f"保存结果失败: {str(e)}")
                return False
        return False
    
    def release_resources(self):
        """释放资源"""
        self.stop_capture()
        
        # 释放摄像头
        for camera in self.cameras:
            try:
                camera.release()
            except:
                pass
        
        self.cameras.clear()
        self.frame_queues.clear()
        
        print("资源已释放")


class StreamProcessor:
    """视频流处理器"""
    
    def __init__(self, feature_matcher=None):
        """初始化视频流处理器"""
        self.feature_matcher = feature_matcher
        self.video_capture = None
        self.is_processing = False
        self.output_writer = None
        
    def process_video_file(self, input_path, output_path, process_func):
        """
        处理视频文件
        
        Args:
            input_path (str): 输入视频路径
            output_path (str): 输出视频路径
            process_func (callable): 处理函数
        """
        try:
            # 打开视频文件
            cap = cv2.VideoCapture(input_path)
            if not cap.isOpened():
                print(f"无法打开视频文件: {input_path}")
                return False
            
            # 获取视频属性
            fps = int(cap.get(cv2.CAP_PROP_FPS))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            print(f"视频信息: {width}x{height}, {fps}fps, {total_frames}帧")
            
            # 创建输出视频写入器
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width*2, height))
            
            frame_count = 0
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # 处理帧
                processed_frame = process_func(frame)
                if processed_frame is not None:
                    # 调整输出尺寸
                    if processed_frame.shape[:2] != (height, width*2):
                        processed_frame = cv2.resize(processed_frame, (width*2, height))
                    out.write(processed_frame)
                
                frame_count += 1
                if frame_count % 30 == 0:
                    progress = (frame_count / total_frames) * 100
                    print(f"处理进度: {progress:.1f}%")
            
            # 释放资源
            cap.release()
            out.release()
            
            print(f"视频处理完成: {output_path}")
            return True
            
        except Exception as e:
            print(f"视频处理失败: {str(e)}")
            return False
    
    def create_time_lapse(self, image_folder, output_path, fps=24):
        """
        创建延时摄影视频
        
        Args:
            image_folder (str): 图像文件夹路径
            output_path (str): 输出视频路径
            fps (int): 帧率
        """
        try:
            import glob
            
            # 获取图像文件
            image_files = []
            for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
                image_files.extend(glob.glob(os.path.join(image_folder, ext)))
            
            if not image_files:
                print("没有找到图像文件")
                return False
            
            image_files.sort()
            print(f"找到 {len(image_files)} 张图像")
            
            # 读取第一张图像获取尺寸
            first_image = cv2.imread(image_files[0])
            if first_image is None:
                print("无法读取第一张图像")
                return False
            
            height, width = first_image.shape[:2]
            
            # 创建视频写入器
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
            
            # 写入图像
            for i, image_file in enumerate(image_files):
                image = cv2.imread(image_file)
                if image is not None:
                    # 调整尺寸
                    if image.shape[:2] != (height, width):
                        image = cv2.resize(image, (width, height))
                    out.write(image)
                
                if i % 10 == 0:
                    progress = (i / len(image_files)) * 100
                    print(f"创建进度: {progress:.1f}%")
            
            out.release()
            print(f"延时摄影视频创建完成: {output_path}")
            return True
            
        except Exception as e:
            print(f"创建延时摄影失败: {str(e)}")
            return False
