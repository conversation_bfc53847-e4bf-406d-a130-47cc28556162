#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
结果查看器 - 展示系统运行结果
"""

import cv2
import numpy as np
import os
import tkinter as tk
from tkinter import ttk
from PIL import Image, ImageTk
import glob


class ResultViewer:
    """结果查看器类"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("图像拼接与布局优化系统 - 结果查看器")
        self.root.geometry("1000x700")
        
        self.current_image = None
        self.setup_ui()
        self.load_results()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧文件列表
        left_frame = ttk.LabelFrame(main_frame, text="结果文件", width=300)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        left_frame.pack_propagate(False)
        
        # 文件树
        self.tree = ttk.Treeview(left_frame, height=20)
        self.tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 绑定选择事件
        self.tree.bind('<<TreeviewSelect>>', self.on_select)
        
        # 右侧图像显示
        right_frame = ttk.LabelFrame(main_frame, text="图像预览")
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 图像信息
        info_frame = ttk.Frame(right_frame)
        info_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.info_label = ttk.Label(info_frame, text="请选择一个图像文件")
        self.info_label.pack(anchor=tk.W)
        
        # 图像显示画布
        self.canvas = tk.Canvas(right_frame, bg="white")
        self.canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 滚动条
        scrollbar_v = ttk.Scrollbar(right_frame, orient=tk.VERTICAL, command=self.canvas.yview)
        scrollbar_h = ttk.Scrollbar(right_frame, orient=tk.HORIZONTAL, command=self.canvas.xview)
        self.canvas.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
    
    def load_results(self):
        """加载结果文件"""
        # 清空树
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 添加测试结果
        if os.path.exists("test_images"):
            test_node = self.tree.insert("", "end", text="测试结果", open=True)
            
            test_files = glob.glob("test_images/*.jpg")
            for file_path in sorted(test_files):
                filename = os.path.basename(file_path)
                self.tree.insert(test_node, "end", text=filename, values=[file_path])
        
        # 添加演示结果
        if os.path.exists("demo_images"):
            demo_node = self.tree.insert("", "end", text="演示结果", open=True)
            
            demo_files = glob.glob("demo_images/*.jpg")
            for file_path in sorted(demo_files):
                filename = os.path.basename(file_path)
                self.tree.insert(demo_node, "end", text=filename, values=[file_path])
    
    def on_select(self, event):
        """处理文件选择事件"""
        selection = self.tree.selection()
        if selection:
            item = selection[0]
            values = self.tree.item(item, "values")
            
            if values:  # 如果有文件路径
                file_path = values[0]
                self.display_image(file_path)
    
    def display_image(self, file_path):
        """显示图像"""
        try:
            # 读取图像
            image = cv2.imread(file_path)
            if image is None:
                self.info_label.config(text="无法读取图像文件")
                return
            
            # 转换颜色空间
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # 获取图像信息
            height, width, channels = image.shape
            file_size = os.path.getsize(file_path)
            
            info_text = f"文件: {os.path.basename(file_path)} | 尺寸: {width}×{height} | 大小: {file_size//1024}KB"
            self.info_label.config(text=info_text)
            
            # 转换为PIL图像
            pil_image = Image.fromarray(image_rgb)
            
            # 调整图像大小以适应画布
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()
            
            if canvas_width > 1 and canvas_height > 1:
                # 计算缩放比例
                scale_w = (canvas_width - 20) / width
                scale_h = (canvas_height - 20) / height
                scale = min(scale_w, scale_h, 1.0)  # 不放大
                
                new_width = int(width * scale)
                new_height = int(height * scale)
                
                pil_image = pil_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # 转换为Tkinter图像
            self.current_image = ImageTk.PhotoImage(pil_image)
            
            # 清空画布并显示图像
            self.canvas.delete("all")
            self.canvas.create_image(
                canvas_width//2, canvas_height//2,
                image=self.current_image, anchor=tk.CENTER
            )
            
        except Exception as e:
            self.info_label.config(text=f"显示图像时出错: {str(e)}")


def main():
    """主函数"""
    root = tk.Tk()
    app = ResultViewer(root)
    root.mainloop()


if __name__ == "__main__":
    main()
