#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图像加载功能
"""

import cv2
import numpy as np
import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.image_processor import ImageProcessor


class ImageLoadTester:
    """图像加载测试器"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("图像加载测试器")
        self.root.geometry("800x600")
        
        self.image_processor = ImageProcessor()
        self.current_image = None
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置界面"""
        # 控制面板
        control_frame = ttk.Frame(self.root)
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.But<PERSON>(control_frame, text="选择图像文件", command=self.load_image).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="测试现有图像", command=self.test_existing_images).pack(side=tk.LEFT, padx=5)
        
        # 状态标签
        self.status_var = tk.StringVar(value="请选择图像文件")
        status_label = ttk.Label(control_frame, textvariable=self.status_var)
        status_label.pack(side=tk.LEFT, padx=20)
        
        # 图像显示区域
        self.canvas = tk.Canvas(self.root, bg="white")
        self.canvas.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 信息显示
        info_frame = ttk.Frame(self.root)
        info_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.info_text = tk.Text(info_frame, height=8)
        self.info_text.pack(fill=tk.X)
    
    def load_image(self):
        """加载图像文件"""
        try:
            file_path = filedialog.askopenfilename(
                title="选择图像文件",
                filetypes=[
                    ("所有支持的图像", "*.jpg *.jpeg *.png *.bmp *.tiff *.tif"),
                    ("JPEG文件", "*.jpg *.jpeg"),
                    ("PNG文件", "*.png"),
                    ("BMP文件", "*.bmp"),
                    ("TIFF文件", "*.tiff *.tif"),
                    ("所有文件", "*.*")
                ]
            )
            
            if not file_path:
                self.status_var.set("未选择文件")
                return
            
            self.status_var.set("正在加载图像...")
            self.log_info(f"尝试加载文件: {file_path}")
            
            # 检查文件
            if not os.path.exists(file_path):
                self.log_info("❌ 文件不存在")
                self.status_var.set("文件不存在")
                return
            
            file_size = os.path.getsize(file_path)
            self.log_info(f"文件大小: {file_size} 字节")
            
            # 使用OpenCV直接加载
            self.log_info("使用OpenCV加载...")
            cv_image = cv2.imread(file_path)
            
            if cv_image is None:
                self.log_info("❌ OpenCV无法读取文件")
                
                # 尝试使用PIL
                self.log_info("尝试使用PIL加载...")
                try:
                    pil_image = Image.open(file_path)
                    cv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
                    self.log_info("✅ PIL加载成功")
                except Exception as e:
                    self.log_info(f"❌ PIL加载失败: {str(e)}")
                    self.status_var.set("图像加载失败")
                    return
            else:
                self.log_info("✅ OpenCV加载成功")
            
            # 使用图像处理器加载
            self.log_info("使用ImageProcessor加载...")
            processor_image = self.image_processor.load_image(file_path)
            
            if processor_image is not None:
                self.log_info("✅ ImageProcessor加载成功")
                self.current_image = processor_image
                
                # 获取图像信息
                info = self.image_processor.get_image_info(processor_image)
                self.log_info(f"图像信息: {info}")
                
                # 显示图像
                self.display_image(processor_image)
                self.status_var.set(f"加载成功 - {info['width']}x{info['height']}")
                
            else:
                self.log_info("❌ ImageProcessor加载失败")
                self.status_var.set("ImageProcessor加载失败")
                
        except Exception as e:
            error_msg = f"加载过程出错: {str(e)}"
            self.log_info(f"❌ {error_msg}")
            self.status_var.set("加载出错")
            messagebox.showerror("错误", error_msg)
    
    def test_existing_images(self):
        """测试现有的图像文件"""
        self.log_info("=== 测试现有图像文件 ===")
        
        # 测试目录
        test_dirs = ["test_images", "demo_images"]
        
        for test_dir in test_dirs:
            if os.path.exists(test_dir):
                self.log_info(f"\n检查目录: {test_dir}")
                
                image_files = []
                for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff']:
                    import glob
                    image_files.extend(glob.glob(os.path.join(test_dir, ext)))
                
                if image_files:
                    for file_path in image_files[:3]:  # 只测试前3个
                        self.log_info(f"测试文件: {os.path.basename(file_path)}")
                        
                        try:
                            image = self.image_processor.load_image(file_path)
                            if image is not None:
                                info = self.image_processor.get_image_info(image)
                                self.log_info(f"  ✅ 成功 - {info['width']}x{info['height']}")
                                
                                # 显示第一张成功加载的图像
                                if self.current_image is None:
                                    self.current_image = image
                                    self.display_image(image)
                                    self.status_var.set(f"测试成功 - {os.path.basename(file_path)}")
                            else:
                                self.log_info(f"  ❌ 加载失败")
                        except Exception as e:
                            self.log_info(f"  ❌ 错误: {str(e)}")
                else:
                    self.log_info(f"  目录中没有图像文件")
            else:
                self.log_info(f"目录不存在: {test_dir}")
    
    def display_image(self, image):
        """显示图像"""
        try:
            # 转换颜色空间
            if len(image.shape) == 3:
                image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            else:
                image_rgb = image
            
            # 转换为PIL图像
            pil_image = Image.fromarray(image_rgb)
            
            # 调整大小
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()
            
            if canvas_width > 1 and canvas_height > 1:
                pil_image.thumbnail((canvas_width-20, canvas_height-20), Image.Resampling.LANCZOS)
            
            # 转换为Tkinter图像
            self.photo = ImageTk.PhotoImage(pil_image)
            
            # 显示
            self.canvas.delete("all")
            self.canvas.create_image(
                canvas_width//2, canvas_height//2,
                image=self.photo, anchor=tk.CENTER
            )
            
        except Exception as e:
            self.log_info(f"显示图像时出错: {str(e)}")
    
    def log_info(self, message):
        """记录信息"""
        self.info_text.insert(tk.END, message + "\n")
        self.info_text.see(tk.END)
        self.root.update()
        print(message)  # 同时输出到控制台


def main():
    """主函数"""
    root = tk.Tk()
    app = ImageLoadTester(root)
    root.mainloop()


if __name__ == "__main__":
    main()
