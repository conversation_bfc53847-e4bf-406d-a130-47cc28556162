#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版图像拼接与布局优化系统GUI
专门解决图像加载问题
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import numpy as np
from PIL import Image, ImageTk
import os
import sys

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.image_processor import ImageProcessor
from src.feature_matcher import FeatureMatcher
from src.image_stitcher import ImageStitcher
from src.layout_optimizer import LayoutOptimizer


class SimpleImageApp:
    """简化版图像处理应用"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("图像拼接与布局优化系统 - 简化版")
        self.root.geometry("1000x700")
        
        # 初始化组件
        self.image_processor = ImageProcessor()
        self.feature_matcher = FeatureMatcher()
        self.image_stitcher = ImageStitcher()
        self.layout_optimizer = LayoutOptimizer()
        
        # 存储图像数据
        self.images = []
        self.current_result = None
        
        self.setup_ui()
        
        # 自动加载测试图像
        self.auto_load_test_images()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧控制面板
        control_frame = ttk.LabelFrame(main_frame, text="控制面板", width=350)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        control_frame.pack_propagate(False)
        
        # 图像加载区域
        load_frame = ttk.LabelFrame(control_frame, text="图像管理")
        load_frame.pack(fill=tk.X, pady=5)
        
        # 按钮行1
        btn_frame1 = ttk.Frame(load_frame)
        btn_frame1.pack(fill=tk.X, pady=2)
        
        ttk.Button(btn_frame1, text="选择图像", command=self.load_images).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame1, text="加载测试图像", command=self.auto_load_test_images).pack(side=tk.LEFT, padx=2)
        ttk.Button(btn_frame1, text="清空", command=self.clear_images).pack(side=tk.LEFT, padx=2)
        
        # 图像列表
        list_frame = ttk.Frame(load_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        ttk.Label(list_frame, text="已加载的图像:").pack(anchor=tk.W)
        
        # 创建列表框和滚动条
        list_container = ttk.Frame(list_frame)
        list_container.pack(fill=tk.BOTH, expand=True)
        
        self.image_listbox = tk.Listbox(list_container, height=8)
        scrollbar = ttk.Scrollbar(list_container, orient=tk.VERTICAL, command=self.image_listbox.yview)
        self.image_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.image_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定选择事件
        self.image_listbox.bind('<<ListboxSelect>>', self.on_image_select)
        
        # 功能按钮区域
        function_frame = ttk.LabelFrame(control_frame, text="功能操作")
        function_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(function_frame, text="图像拼接", command=self.stitch_images).pack(fill=tk.X, pady=2)
        ttk.Button(function_frame, text="网格布局", command=self.grid_layout).pack(fill=tk.X, pady=2)
        ttk.Button(function_frame, text="优化布局", command=self.optimize_layout).pack(fill=tk.X, pady=2)
        
        # 参数设置
        params_frame = ttk.LabelFrame(control_frame, text="参数设置")
        params_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(params_frame, text="特征算法:").pack(anchor=tk.W)
        self.algorithm_var = tk.StringVar(value="SIFT")
        algorithm_combo = ttk.Combobox(params_frame, textvariable=self.algorithm_var, 
                                     values=["SIFT", "ORB", "AKAZE"], state="readonly")
        algorithm_combo.pack(fill=tk.X, pady=2)
        
        # 右侧显示区域
        display_frame = ttk.LabelFrame(main_frame, text="图像显示")
        display_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 图像信息
        self.info_label = ttk.Label(display_frame, text="请选择或加载图像")
        self.info_label.pack(anchor=tk.W, padx=5, pady=2)
        
        # 图像显示画布
        canvas_frame = ttk.Frame(display_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.canvas = tk.Canvas(canvas_frame, bg="white")
        v_scrollbar = ttk.Scrollbar(canvas_frame, orient=tk.VERTICAL, command=self.canvas.yview)
        h_scrollbar = ttk.Scrollbar(canvas_frame, orient=tk.HORIZONTAL, command=self.canvas.xview)
        
        self.canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def load_images(self):
        """加载图像文件"""
        try:
            file_paths = filedialog.askopenfilenames(
                title="选择图像文件",
                filetypes=[
                    ("所有支持的图像", "*.jpg *.jpeg *.png *.bmp *.tiff *.tif"),
                    ("JPEG文件", "*.jpg *.jpeg"),
                    ("PNG文件", "*.png"),
                    ("BMP文件", "*.bmp"),
                    ("TIFF文件", "*.tiff *.tif"),
                    ("所有文件", "*.*")
                ]
            )
            
            if not file_paths:
                self.status_var.set("未选择文件")
                return
            
            self.status_var.set("正在加载图像...")
            self.root.update()
            
            loaded_count = 0
            for file_path in file_paths:
                try:
                    print(f"加载文件: {file_path}")
                    
                    if not os.path.exists(file_path):
                        print(f"文件不存在: {file_path}")
                        continue
                    
                    # 加载图像
                    image = self.image_processor.load_image(file_path)
                    if image is not None:
                        # 检查是否已经加载过
                        filename = os.path.basename(file_path)
                        if not any(img['name'] == filename for img in self.images):
                            self.images.append({
                                'path': file_path,
                                'image': image,
                                'name': filename
                            })
                            self.image_listbox.insert(tk.END, filename)
                            loaded_count += 1
                            print(f"成功加载: {filename}")
                        else:
                            print(f"图像已存在: {filename}")
                    else:
                        print(f"加载失败: {file_path}")
                        
                except Exception as e:
                    print(f"加载图像时出错: {str(e)}")
                    messagebox.showerror("错误", f"加载 {os.path.basename(file_path)} 失败:\n{str(e)}")
            
            if loaded_count > 0:
                self.status_var.set(f"成功加载 {loaded_count} 张图像，总计 {len(self.images)} 张")
                # 选择第一张图像
                if self.image_listbox.size() > 0:
                    self.image_listbox.selection_set(0)
                    self.on_image_select(None)
            else:
                self.status_var.set("没有成功加载任何新图像")
                
        except Exception as e:
            print(f"文件对话框错误: {str(e)}")
            messagebox.showerror("错误", f"打开文件对话框时出错: {str(e)}")
            self.status_var.set("加载失败")
    
    def auto_load_test_images(self):
        """自动加载测试图像"""
        try:
            self.status_var.set("正在加载测试图像...")
            
            # 查找测试图像
            test_dirs = ["test_images", "demo_images"]
            loaded_count = 0
            
            for test_dir in test_dirs:
                if os.path.exists(test_dir):
                    import glob
                    image_files = []
                    for pattern in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
                        image_files.extend(glob.glob(os.path.join(test_dir, pattern)))
                    
                    for file_path in image_files[:5]:  # 最多加载5张
                        try:
                            filename = os.path.basename(file_path)
                            
                            # 检查是否已经加载过
                            if any(img['name'] == filename for img in self.images):
                                continue
                            
                            image = self.image_processor.load_image(file_path)
                            if image is not None:
                                self.images.append({
                                    'path': file_path,
                                    'image': image,
                                    'name': filename
                                })
                                self.image_listbox.insert(tk.END, filename)
                                loaded_count += 1
                                
                        except Exception as e:
                            print(f"加载测试图像失败: {str(e)}")
            
            if loaded_count > 0:
                self.status_var.set(f"自动加载了 {loaded_count} 张测试图像")
                # 选择第一张图像
                if self.image_listbox.size() > 0:
                    self.image_listbox.selection_set(0)
                    self.on_image_select(None)
            else:
                self.status_var.set("没有找到测试图像")
                
        except Exception as e:
            print(f"自动加载测试图像出错: {str(e)}")
            self.status_var.set("自动加载失败")
    
    def clear_images(self):
        """清空图像"""
        self.images.clear()
        self.image_listbox.delete(0, tk.END)
        self.canvas.delete("all")
        self.current_result = None
        self.info_label.config(text="请选择或加载图像")
        self.status_var.set("已清空所有图像")
    
    def on_image_select(self, event):
        """处理图像选择事件"""
        selection = self.image_listbox.curselection()
        if selection:
            index = selection[0]
            if 0 <= index < len(self.images):
                image_data = self.images[index]
                self.display_image(image_data['image'])
                
                # 更新信息
                info = self.image_processor.get_image_info(image_data['image'])
                self.info_label.config(text=f"{image_data['name']} - {info['width']}×{info['height']}")
    
    def display_image(self, image):
        """显示图像"""
        try:
            if image is None:
                return
            
            # 转换颜色空间
            if len(image.shape) == 3:
                image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            else:
                image_rgb = image
            
            # 转换为PIL图像
            pil_image = Image.fromarray(image_rgb)
            
            # 调整大小以适应画布
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()
            
            if canvas_width > 1 and canvas_height > 1:
                # 计算缩放比例
                scale_w = (canvas_width - 20) / pil_image.width
                scale_h = (canvas_height - 20) / pil_image.height
                scale = min(scale_w, scale_h, 1.0)  # 不放大
                
                if scale < 1.0:
                    new_width = int(pil_image.width * scale)
                    new_height = int(pil_image.height * scale)
                    pil_image = pil_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # 转换为Tkinter图像
            self.photo = ImageTk.PhotoImage(pil_image)
            
            # 清空画布并显示图像
            self.canvas.delete("all")
            self.canvas.create_image(
                canvas_width//2, canvas_height//2,
                image=self.photo, anchor=tk.CENTER
            )
            
        except Exception as e:
            print(f"显示图像时出错: {str(e)}")
            self.info_label.config(text=f"显示图像出错: {str(e)}")
    
    def stitch_images(self):
        """执行图像拼接"""
        if len(self.images) < 2:
            messagebox.showwarning("警告", "至少需要2张图像进行拼接")
            return
        
        try:
            self.status_var.set("正在进行图像拼接...")
            self.root.update()
            
            # 设置算法
            self.feature_matcher.set_algorithm(self.algorithm_var.get())
            
            # 提取图像
            image_list = [img['image'] for img in self.images]
            
            # 执行拼接
            result = self.image_stitcher.stitch_images(image_list, self.feature_matcher)
            
            if result is not None:
                self.current_result = result
                self.display_image(result)
                info = self.image_processor.get_image_info(result)
                self.info_label.config(text=f"拼接结果 - {info['width']}×{info['height']}")
                self.status_var.set("图像拼接完成")
            else:
                messagebox.showerror("错误", "图像拼接失败")
                self.status_var.set("拼接失败")
                
        except Exception as e:
            print(f"拼接过程出错: {str(e)}")
            messagebox.showerror("错误", f"拼接过程出错: {str(e)}")
            self.status_var.set("拼接出错")
    
    def grid_layout(self):
        """网格布局"""
        if len(self.images) < 2:
            messagebox.showwarning("警告", "至少需要2张图像进行布局")
            return
        
        try:
            self.status_var.set("正在生成网格布局...")
            self.root.update()
            
            image_list = [img['image'] for img in self.images]
            result = self.layout_optimizer.optimize_layout(image_list, method='grid')
            
            if result is not None:
                self.current_result = result
                self.display_image(result)
                info = self.image_processor.get_image_info(result)
                self.info_label.config(text=f"网格布局 - {info['width']}×{info['height']}")
                self.status_var.set("网格布局完成")
            else:
                messagebox.showerror("错误", "网格布局失败")
                self.status_var.set("布局失败")
                
        except Exception as e:
            print(f"布局过程出错: {str(e)}")
            messagebox.showerror("错误", f"布局过程出错: {str(e)}")
            self.status_var.set("布局出错")
    
    def optimize_layout(self):
        """优化布局"""
        if len(self.images) < 2:
            messagebox.showwarning("警告", "至少需要2张图像进行布局优化")
            return
        
        try:
            self.status_var.set("正在优化布局（可能需要1-2分钟）...")
            self.root.update()
            
            image_list = [img['image'] for img in self.images]
            result = self.layout_optimizer.optimize_layout(image_list, method='genetic')
            
            if result is not None:
                self.current_result = result
                self.display_image(result)
                info = self.image_processor.get_image_info(result)
                self.info_label.config(text=f"优化布局 - {info['width']}×{info['height']}")
                self.status_var.set("布局优化完成")
            else:
                messagebox.showerror("错误", "布局优化失败")
                self.status_var.set("优化失败")
                
        except Exception as e:
            print(f"优化过程出错: {str(e)}")
            messagebox.showerror("错误", f"优化过程出错: {str(e)}")
            self.status_var.set("优化出错")


def main():
    """主函数"""
    root = tk.Tk()
    app = SimpleImageApp(root)
    root.mainloop()


if __name__ == "__main__":
    main()
